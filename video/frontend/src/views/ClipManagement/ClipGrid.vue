<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center">
          <h1 class="text-3xl font-bold text-gray-900">片段管理</h1>
          <div class="flex space-x-3">
            <button
              @click="generateClips"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              生成片段
            </button>
            <button
              @click="showFilterModal = true"
              class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              筛选
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div v-if="activeFilters.length > 0" class="bg-white border-b">
      <div class="max-w-7xl mx-auto py-3 px-4 sm:px-6 lg:px-8">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">筛选条件:</span>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="filter in activeFilters"
              :key="filter.key"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {{ filter.label }}
              <button
                @click="removeFilter(filter.key)"
                class="ml-1 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          </div>
          <button
            @click="clearFilters"
            class="text-sm text-gray-500 hover:text-gray-700"
          >
            清除全部
          </button>
        </div>
      </div>
    </div>

    <!-- 片段网格 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p class="mt-4 text-gray-500">加载中...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="clips.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">暂无片段</h3>
          <p class="mt-1 text-sm text-gray-500">开始分析视频以生成智能片段</p>
          <div class="mt-6">
            <button
              @click="generateClips"
              class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              生成片段
            </button>
          </div>
        </div>

        <!-- 片段网格 -->
        <div v-else class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          <div
            v-for="clip in clips"
            :key="clip.id"
            class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer"
            @click="viewClip(clip.id)"
          >
            <!-- 缩略图 -->
            <div class="aspect-video bg-gray-200 relative">
              <img
                v-if="clip.thumbnail_path"
                :src="clip.thumbnail_path"
                :alt="clip.title"
                class="w-full h-full object-cover"
              />
              <div v-else class="w-full h-full flex items-center justify-center">
                <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              
              <!-- 时长标签 -->
              <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                {{ formatDuration(clip.duration) }}
              </div>
              
              <!-- 质量评分 -->
              <div class="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                {{ Math.round(clip.quality_score || 0) }}分
              </div>
            </div>
            
            <!-- 片段信息 -->
            <div class="p-4">
              <h3 class="text-sm font-medium text-gray-900 truncate">{{ clip.title || '未命名片段' }}</h3>
              <p class="mt-1 text-xs text-gray-500 line-clamp-2">
                {{ clip.description || '暂无描述' }}
              </p>
              
              <!-- 标签 -->
              <div v-if="clip.tags && clip.tags.length > 0" class="mt-2 flex flex-wrap gap-1">
                <span
                  v-for="tag in clip.tags.slice(0, 3)"
                  :key="tag"
                  class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {{ tag }}
                </span>
                <span
                  v-if="clip.tags.length > 3"
                  class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                >
                  +{{ clip.tags.length - 3 }}
                </span>
              </div>
              
              <!-- 片段类型和重要性 -->
              <div class="mt-3 flex justify-between items-center text-xs text-gray-500">
                <span class="capitalize">{{ getClipTypeText(clip.clip_type) }}</span>
                <div class="flex items-center">
                  <svg class="h-3 w-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span>{{ Math.round(clip.importance_score || 0) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选模态框 -->
    <div
      v-if="showFilterModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showFilterModal = false"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">筛选片段</h3>
          
          <form @submit.prevent="applyFilters">
            <div class="space-y-4">
              <!-- 片段类型 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">片段类型</label>
                <select
                  v-model="filters.clip_type"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">全部类型</option>
                  <option value="character">角色片段</option>
                  <option value="scene">场景片段</option>
                  <option value="dialogue">对话片段</option>
                  <option value="action">动作片段</option>
                  <option value="emotion">情感片段</option>
                </select>
              </div>
              
              <!-- 时长范围 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">时长范围（秒）</label>
                <div class="flex space-x-2">
                  <input
                    v-model.number="filters.min_duration"
                    type="number"
                    placeholder="最小"
                    min="0"
                    class="flex-1 px-2 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                  />
                  <input
                    v-model.number="filters.max_duration"
                    type="number"
                    placeholder="最大"
                    min="0"
                    class="flex-1 px-2 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                  />
                </div>
              </div>
              
              <!-- 质量评分 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">最低质量评分</label>
                <input
                  v-model.number="filters.min_quality"
                  type="range"
                  min="0"
                  max="100"
                  class="w-full"
                />
                <div class="text-center text-sm text-gray-500">{{ filters.min_quality }}分</div>
              </div>
              
              <!-- 重要性评分 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">最低重要性评分</label>
                <input
                  v-model.number="filters.min_importance"
                  type="range"
                  min="0"
                  max="100"
                  class="w-full"
                />
                <div class="text-center text-sm text-gray-500">{{ filters.min_importance }}分</div>
              </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                @click="showFilterModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"
              >
                应用筛选
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useClipStore } from '@/stores/clips'

const router = useRouter()
const appStore = useAppStore()
const clipStore = useClipStore()

// 响应式数据
const loading = ref(false)
const clips = ref([])
const showFilterModal = ref(false)
const filters = ref({
  clip_type: '',
  min_duration: null,
  max_duration: null,
  min_quality: 0,
  min_importance: 0
})
const activeFilters = ref([])



// 方法
const loadClips = async () => {
  loading.value = true
  try {
    clips.value = await clipStore.fetchClips()
  } catch (error) {
    appStore.showError('加载失败', '无法加载片段列表')
  } finally {
    loading.value = false
  }
}

const generateClips = async () => {
  try {
    // 这里需要一个视频ID，可以从路由参数或其他地方获取
    // 暂时使用默认值，实际应用中需要根据具体需求调整
    const videoId = 1 // 或者从路由参数获取
    await clipStore.generateClips(videoId)
    appStore.showSuccess('生成中', '正在生成智能片段...')
    // 重新加载片段列表
    await loadClips()
  } catch (error) {
    appStore.showError('生成失败', '无法生成片段')
  }
}

const viewClip = (clipId) => {
  router.push(`/clips/${clipId}`)
}

const applyFilters = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const queryParams = {}
    if (filters.value.clip_type) {
      queryParams.clip_type = filters.value.clip_type
    }
    if (filters.value.min_duration) {
      queryParams.min_duration = filters.value.min_duration
    }
    if (filters.value.max_duration) {
      queryParams.max_duration = filters.value.max_duration
    }
    if (filters.value.min_quality > 0) {
      queryParams.min_quality = filters.value.min_quality
    }
    if (filters.value.min_importance > 0) {
      queryParams.min_importance = filters.value.min_importance
    }

    // 使用筛选参数获取片段
    clips.value = await clipStore.fetchClips(queryParams)
    updateActiveFilters()
    showFilterModal.value = false
  } catch (error) {
    appStore.showError('筛选失败', '无法应用筛选条件')
  } finally {
    loading.value = false
  }
}

const updateActiveFilters = () => {
  const active = []
  
  if (filters.value.clip_type) {
    active.push({
      key: 'clip_type',
      label: `类型: ${getClipTypeText(filters.value.clip_type)}`
    })
  }
  
  if (filters.value.min_duration) {
    active.push({
      key: 'min_duration',
      label: `最短: ${filters.value.min_duration}秒`
    })
  }
  
  if (filters.value.max_duration) {
    active.push({
      key: 'max_duration',
      label: `最长: ${filters.value.max_duration}秒`
    })
  }
  
  if (filters.value.min_quality > 0) {
    active.push({
      key: 'min_quality',
      label: `质量: ≥${filters.value.min_quality}分`
    })
  }
  
  if (filters.value.min_importance > 0) {
    active.push({
      key: 'min_importance',
      label: `重要性: ≥${filters.value.min_importance}分`
    })
  }
  
  activeFilters.value = active
}

const removeFilter = (key) => {
  filters.value[key] = key.includes('min_') ? 0 : ''
  updateActiveFilters()
}

const clearFilters = () => {
  filters.value = {
    clip_type: '',
    min_duration: null,
    max_duration: null,
    min_quality: 0,
    min_importance: 0
  }
  activeFilters.value = []
}

const getClipTypeText = (type) => {
  const types = {
    character: '角色',
    scene: '场景',
    dialogue: '对话',
    action: '动作',
    emotion: '情感'
  }
  return types[type] || type
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  loadClips()
})
</script>
